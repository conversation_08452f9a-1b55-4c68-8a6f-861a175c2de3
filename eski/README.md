# SorcerioModules - Social Media Automation Tool

SorcerioModules is a comprehensive social media automation tool that allows you to download content from Instagram, Twitter, and YouTube, and automatically post them to your social media accounts with scheduling capabilities.

## Features

- **Multi-Platform Support**: Download content from Instagram, Twitter, and YouTube
- **Automated Posting**: Schedule and automatically post content to Instagram and Twitter
- **Modern UI**: PyQt5-based user interface with 3-deck layout
- **Profile Management**: Manage multiple social media profiles with individual settings
- **Content Scheduling**: Set specific times for posting content
- **Live Feed**: Real-time monitoring of download and upload activities
- **Statistics**: Track posting statistics for all your profiles
- **Thumbnail Generation**: Automatic thumbnail creation for videos
- **Session Management**: Persistent login sessions for social media platforms

## System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Python**: 3.8 or higher
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: At least 2GB free space
- **Internet**: Stable internet connection required

## Installation

### Option 1: Automatic Installation (Recommended)

1. **Download** the SorcerioModules package
2. **Extract** all files to a folder (e.g., `C:\SorcerioModules`)
3. **Run** `install_requirements.bat` as Administrator
4. **Wait** for all dependencies to install
5. **Run** `setup_checker.py` to verify installation
6. **Start** the application with `python main.py`

### Option 2: Manual Installation

1. **Install Python 3.8+** from [python.org](https://python.org)
   - ✅ Make sure to check "Add Python to PATH" during installation
2. **Open Command Prompt** as Administrator
3. **Navigate** to the SorcerioModules folder
4. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
5. **Run setup checker**:
   ```bash
   python setup_checker.py
   ```
6. **Start the application**:
   ```bash
   python main.py
   ```

## First Run Setup

When you run SorcerioModules for the first time:

1. **Automatic Setup**: The application will create necessary directories and configuration files
2. **Profile Creation**: Default profile templates will be created for Instagram and Twitter
3. **Browser Setup**: Portable Chrome browser will be downloaded if needed
4. **FFmpeg Setup**: Video processing tools will be downloaded automatically

## Configuration

### Profile Setup

1. **Open** the application
2. **Click** on profile management in the UI
3. **Configure** your social media accounts:
   - Username and password
   - Posting schedule (by day of week)
   - Hashtags
   - Content links

### Adding Content

1. **Select** a profile
2. **Click** "Link Ekle" (Add Link)
3. **Paste** Instagram, Twitter, or YouTube URLs
4. **Save** the configuration

### Scheduling Posts

1. **Open** profile settings
2. **Set** posting times for each day of the week
3. **Save** the schedule
4. The application will automatically post content at scheduled times

## Usage

### Main Interface

The application features a 3-deck interface:

1. **Statistics Deck**: View posting statistics and profile performance
2. **Can Bars Deck**: Monitor content queue and posting progress
3. **Live Feed Deck**: Real-time activity monitoring

### Downloading Content

- Content is automatically downloaded when you add links to profiles
- Downloaded content is stored in the `videos/` directory
- Thumbnails are automatically generated for videos

### Posting Content

- Content is posted automatically based on your schedule
- Manual posting is also available through the interface
- Posted content is tracked in the statistics

## Troubleshooting

### Common Issues

**"Python is not installed or not in PATH"**
- Install Python from python.org
- Make sure to check "Add Python to PATH" during installation
- Restart your computer after installation

**"Critical dependencies missing"**
- Run `install_requirements.bat` as Administrator
- If that fails, try: `pip install -r requirements.txt`

**"Chrome browser not found"**
- The application will download portable Chrome automatically
- Ensure you have internet connection on first run
- Check Windows Defender/Antivirus isn't blocking downloads

**"FFmpeg not found"**
- FFmpeg is downloaded automatically on first use
- Ensure internet connection is available
- Check antivirus software isn't blocking the download

### Getting Help

1. **Run** `setup_checker.py` to diagnose issues
2. **Check** the log file in `videos/social_downloader.log`
3. **Ensure** all dependencies are installed correctly
4. **Verify** internet connection is stable

## File Structure

```
SorcerioModules/
├── main.py                     # Main application entry point
├── setup_checker.py            # Environment validation tool
├── install_requirements.bat    # Dependency installer
├── requirements.txt            # Python dependencies
├── configuration/              # Profile and settings storage
│   ├── instagram/             # Instagram profiles
│   └── twitter/               # Twitter profiles
├── videos/                    # Downloaded content storage
│   ├── instagramdownloaded/   # Instagram content
│   ├── twitterdownloaded/     # Twitter content
│   └── youtubedownloaded/     # YouTube content
├── portable_chromium/         # Portable browser
└── live_feed_thumbnails/      # UI thumbnails
```

## Security Notes

- **Credentials**: Your social media credentials are stored locally in encrypted format
- **Sessions**: Login sessions are saved to avoid repeated authentication
- **Privacy**: No data is sent to external servers except for content downloading
- **Antivirus**: Some antivirus software may flag browser automation tools

## Support

For technical support or questions:
- Check the troubleshooting section above
- Run `setup_checker.py` for diagnostic information
- Review log files for detailed error messages

## License

This software is provided for personal use. Please respect the terms of service of social media platforms when using this tool.

---

**Version**: 1.0  
**Last Updated**: 2025  
**Compatibility**: Windows 10/11, Python 3.8+
