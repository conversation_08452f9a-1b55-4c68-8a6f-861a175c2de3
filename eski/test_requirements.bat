@echo off
echo Testing requirements.txt installation...
echo.

REM Create test virtual environment
python -m venv test_env
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
call test_env\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ERROR: Failed to upgrade pip
    pause
    exit /b 1
)

REM Install requirements
pip install -r requirements.txt --no-warn-script-location
if errorlevel 1 (
    echo ERROR: Failed to install requirements
    pause
    exit /b 1
)

REM Run setup checker
python setup_checker.py
if errorlevel 1 (
    echo ERROR: Setup checker failed
    pause
    exit /b 1
)

echo.
echo ✅ All requirements installed successfully!
echo.
pause

REM Cleanup
deactivate
rmdir /s /q test_env
